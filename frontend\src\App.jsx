import React from "react";
import Header from "./components/Header";
import Modal from "./components/Modal";
import Summary from "./components/Summary";
import { SummaryProvider } from "./context/SummaryContext";

const App = () => {
  return (
    <SummaryProvider>
      <div className="flex flex-col h-screen bg-gray-100">
        <Header />
        {/* Fixed upload cards positioned below header */}
        <div className="top-24 left-0 right-0 z-40 bg-gray-100 shadow-sm">
          <div className="container mx-auto px-4 py-2">
            <Modal />
          </div>
        </div>
        {/* Main content with top padding to account for fixed upload cards */}
        <main className="flex-1 overflow-y-auto pt-32">
          <div className="container mx-auto px-4">
            <div className="mt-6">
              <Summary />
            </div>
          </div>
        </main>
      </div>
    </SummaryProvider>
  );
};

export default App;




