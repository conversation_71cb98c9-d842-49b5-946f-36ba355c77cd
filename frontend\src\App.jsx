import React from "react";
import Header from "./components/Header";
import Modal from "./components/Modal";
import Summary from "./components/Summary";
import { SummaryProvider } from "./context/SummaryContext";

const App = () => {
  return (
    <SummaryProvider>
      <div className="flex flex-col h-screen bg-gray-100">
        <Header />
        <main className="flex-1 overflow-y-auto pt-4">
          <div className="container mx-auto px-4">
            <div>
              <Modal />
            </div>
            <div className="mt-6">
              <Summary />
            </div>
          </div>
        </main>
      </div>
    </SummaryProvider>
  );
};

export default App;




